# current_app/blueprints/analysis/tfidf_routes.py
import json
import logging
import os
import random
import traceback
import uuid

from ...services.file_processor import get_file_columns
from flask import Blueprint, current_app, jsonify, request, send_file, url_for
from werkzeug.utils import secure_filename

from ...services.json_session_service import (get_session_value,
                                              set_session_value)
from ...services.nlp_service import (NGramAnalyzer,
                                     WordCloudGenerator, get_file_path)
from ...services.session_service import get_or_create_session_id
from ...utils.error_handlers import (
    AnalysisError, FileError, SessionError, ValidationError, handle_exceptions,
    log_error, safe_get_form_data, validate_file_exists
)
from . import ngram_bp


# Configure CORS for React frontend with HTTPS support
@ngram_bp.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    # Set UTF-8 charset for JSON responses to properly handle Korean characters
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'

    return response


@ngram_bp.route("/analyze", methods=["POST"])
@handle_exceptions(include_traceback=True, log_errors=True)
def analyze():
    """파일 분석 API"""
    # This analyze the words and generate a wordcloud from all words
    session_id = get_or_create_session_id()
    if not session_id:
        raise SessionError("유효한 세션 ID를 생성할 수 없습니다.")

    # 필수 파라미터 확인
    column_name = safe_get_form_data("column_name", required=True)

    # n-gram 설정 가져오기 및 검증
    n_gram_str = safe_get_form_data("n_gram", default="2")
    max_features_str = safe_get_form_data("max_features", default="3000")

    try:
        n_gram = int(n_gram_str)
        if n_gram < 1 or n_gram > 5:
            raise ValidationError("N-gram 값은 1-5 사이여야 합니다.")
    except ValueError:
        raise ValidationError("N-gram 값은 숫자여야 합니다.")

    try:
        max_features = int(max_features_str)
        if max_features < 100 or max_features > 10000:
            raise ValidationError("최대 특성 수는 100-10000 사이여야 합니다.")
    except ValueError:
        raise ValidationError("최대 특성 수는 숫자여야 합니다.")

    # 워드클라우드 옵션 (있는 경우에만)
    selection_type = safe_get_form_data("selection_type", default="manual")
    max_words = safe_get_form_data("max_words")
    cloud_shape = safe_get_form_data("cloud_shape", default="rectangle")
    cloud_color = safe_get_form_data("cloud_color", default="viridis")
    selected_words = safe_get_form_data("selected_words")

    # 파일 정보 가져오기
    filename = get_session_value(session_id, "uploaded_file")
    if not filename:
        raise SessionError("업로드된 파일이 없습니다.", session_id=session_id)

    file_path = get_file_path(session_id, filename, "uploaded")
    if not file_path:
        raise FileError("분석할 파일을 찾을 수 없습니다.")

    # 파일 존재 확인
    validate_file_exists(file_path)

    try:
        # 분석 실행
        log_error(f"Starting N-gram analysis for session {session_id}: {filename}", level=20)  # INFO level
        analyzer = NGramAnalyzer()
        result = analyzer.analyze(
            file_path=file_path,
            column_name=column_name,
            n_gram=n_gram_str,
            max_features=max_features_str,
            selection_type=selection_type,
            max_words=max_words,
            cloud_shape=cloud_shape,
            cloud_color=cloud_color,
            selected_words=selected_words,
        )

        if "error" in result:
            raise AnalysisError(f"N-gram 분석 중 오류가 발생했습니다: {result['error']}", analysis_type="ngram")

        if not result:
            raise AnalysisError("분석 결과가 비어있습니다.", analysis_type="ngram")

    except Exception as e:
        if isinstance(e, (AnalysisError, ValidationError, FileError, SessionError)):
            raise
        raise AnalysisError(f"N-gram 분석 중 예상치 못한 오류가 발생했습니다: {str(e)}", analysis_type="ngram")

    # Add download URLs for all result files
    if 'output_file' in result:
        result["excel_download_url"] = url_for(
            "process.serve_file",
            filepath=result["output_file"],
            download="true",
            session_id=session_id,
            _external=True,
        )

    if 'file_path' in result:
        result["wordcloud_download_url"] = url_for(
            "process.serve_file",
            filepath=result["file_path"],
            download="true",
            session_id=session_id,
            _external=True,
        )

    log_error(f"N-gram analysis completed successfully for session {session_id}", level=20)  # INFO level
    return jsonify(result)


@ngram_bp.route("/get_word_data", methods=["POST"])
def get_word_data():
    """단어 목록 데이터만 반환하는 API (수동 선택 모드용)"""
    # This analyze the words return a few (300) words and their count. The user can then use these words to manually generate a wordcloud
    try:
        session_id = get_or_create_session_id()
        column_name = request.form.get("column_name")
        if not column_name:
            return jsonify({"error": "분석할 컬럼을 선택해주세요"}), 400

        # n-gram 설정 가져오기
        n_gram = request.form.get("n_gram", "2")  # 기본값 2
        max_features = request.form.get("max_features", "3000")

        # 파일 저장
        filename = get_session_value(session_id, "uploaded_file")
        file_path = get_file_path(session_id, filename, "uploaded")

        # 분석 실행 - 수동 선택 모드로 실행하여 단어 데이터만 반환
        analyzer = NGramAnalyzer()
        result = analyzer.analyze(
            file_path=file_path,
            column_name=column_name,
            n_gram=n_gram,
            max_features=max_features,
            selection_type="manual",
        )

        if "error" in result:
            return jsonify(result), 400

        # Add download URLs for all result files
        if 'output_file' in result:
            result["excel_download_url"] = url_for(
                "process.serve_file",
                filepath=result["output_file"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        if 'file_path' in result:
            result["wordcloud_download_url"] = url_for(
                "process.serve_file",
                filepath=result["file_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                ** result
            }
        )
    except Exception as e:
        current_app.logger.error(f"단어 데이터 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@ngram_bp.route("/generate_wordcloud", methods=["POST"])
def generate_wordcloud():
    """선택한 단어로 워드클라우드 생성 API"""
    # This generate a wordcloud from a list of words. The user gets the words by calling the get_word_data endpoint
    session_id = get_or_create_session_id()
    try:
        selected_words = request.form.get("selected_words", "[]")
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")
        n_gram = request.form.get("n_gram", "2")  # n-gram 값 추가

        word_list = json.loads(selected_words)
        if not word_list or len(word_list) < 10:
            return jsonify({"error": "최소 10개 이상의 단어를 선택해주세요."}), 400

        # 결과 파일용 고유 파일명 생성
        filename = f"ngram_{n_gram}_wordcloud_{uuid.uuid4().hex[:8]}"
        wordcloud_filename = f"{filename}.png"
        
        count_dict = {word: random.randint(10, 100) for word in word_list}
        wordcloud_generator = WordCloudGenerator(analysis_type="ngram")
        wordcloud_result = wordcloud_generator.generate_wordcloud(
            count_dict=count_dict,
            filename_base=wordcloud_filename,
            cloud_shape=cloud_shape or "rectangle",
            cloud_color=cloud_color or "viridis",
            session_id=session_id,
        )
        # wordcloud_path = wordcloud_result["file_path"]
        if 'file_path' in wordcloud_result:
            wordcloud_result["download_url"] = url_for(
                "process.serve_file",
                filepath=wordcloud_result["file_path"],
                download="true",
                session_id=session_id,
                analysis_category=current_app.config["WORD_CLOUD_FOLDER"],
                _external=True,
            )
        # Add wordcloud result to main result
        if wordcloud_result.get("success"):
            wordcloud_result["wordcloud_file"] = wordcloud_result["wordcloud_file"]
            print(
                f"워드클라우드 생성 성공: {wordcloud_result['wordcloud_file']}"
            )
        
        # 단어-빈도 사전 생성 (다양한 크기로 표현하기 위해 랜덤 빈도 부여)
        

        # 워드클라우드 생성 - 개선된 함수 사용
        

        return jsonify(wordcloud_result)
    except Exception as e:
        # logging.logger.error(f"워드클라우드 생성 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500
