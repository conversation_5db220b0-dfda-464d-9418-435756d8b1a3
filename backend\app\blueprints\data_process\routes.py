# app/blueprints/api/routes.py
import os
import time

from flask import (abort, current_app, jsonify, redirect, request, send_file,
                   send_from_directory, url_for)

from ...services.file_processor import (process_file,
                                        read_file)
from ...services.json_session_service import (get_session_value,
                                              set_session_value)
from ...services.nlp_service import ( get_language_groups,
                                     get_pos_tags, get_spacy_status)
from ...services.session_service import (get_or_create_session_id,
                                         get_session_by_id, require_session)
from ...utils.error_handlers import (
    AnalysisError, FileError, SessionError, ValidationError, handle_exceptions,
    log_error, safe_get_form_data, validate_file_exists, validate_required_fields,
    validate_session_data
)
from . import process as step2


# Configure CORS for React frontend with HTTPS support
@step2.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    # Set UTF-8 charset for JSON responses to properly handle Korean characters
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'

    return response


@step2.route("/language-groups", methods=["GET"])
@handle_exceptions(include_traceback=True, log_errors=True)
def language_groups():
    """Get available language groups and analyzers"""
    try:
        groups = get_language_groups()
        if not groups:
            raise AnalysisError("언어 그룹을 가져올 수 없습니다.")
        return jsonify(groups)
    except Exception as e:
        raise AnalysisError(f"언어 그룹 조회 중 오류가 발생했습니다: {str(e)}")


@step2.route("/pos-tags/<language>/<analyzer>", methods=["GET"])
@handle_exceptions(include_traceback=True, log_errors=True)
def pos_tags(language, analyzer):
    """Get POS tags for the selected language and analyzer"""
    # Validate input parameters
    if not language or not analyzer:
        raise ValidationError("언어와 분석기가 필요합니다.")

    try:
        tags = get_pos_tags(language, analyzer)
        if not tags:
            raise AnalysisError(f"지원되지 않는 언어/분석기 조합입니다: {language}/{analyzer}")
        return jsonify(tags)
    except Exception as e:
        raise AnalysisError(f"POS 태그 조회 중 오류가 발생했습니다: {str(e)}")


@step2.route("/spacy-status", methods=["GET"])
@handle_exceptions(include_traceback=True, log_errors=True)
def spacy_status():
    """Check if spaCy model is installed"""
    try:
        status = get_spacy_status()
        return jsonify(status)
    except Exception as e:
        raise AnalysisError(f"spaCy 상태 확인 중 오류가 발생했습니다: {str(e)}")


@step2.route("/columns", methods=["GET"])
@require_session
@handle_exceptions(include_traceback=True, log_errors=True)
def columns():
    """Get columns from an uploaded Excel file"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Get or create session ID from the session or use the provided one
    if query_session_id:
        # Verify that the session exists
        session_data = get_session_by_id(query_session_id)
        if not session_data:
            raise SessionError(f"Session with ID {query_session_id} not found", session_id=query_session_id)
        session_id = query_session_id
    else:
        # Get or create session ID
        session_id = get_or_create_session_id()

    # Check if a file has been uploaded
    filename = get_session_value(session_id, "uploaded_file")
    if not filename:
        raise SessionError("파일이 업로드되지 않았습니다.", session_id=session_id)

    # Get session folder and file path
    upload_folder = current_app.config["UPLOAD_FOLDER"]
    session_folder = os.path.join(upload_folder, session_id)
    filepath = os.path.join(session_folder, filename)

    # Validate file exists
    validate_file_exists(filepath)

    try:
        # Read file and get columns
        data = read_file(filepath)

        if data.empty:
            raise ValidationError("파일이 비어있습니다.")

        columns = data.columns.to_list()
        if not columns:
            raise ValidationError("파일에 유효한 컬럼이 없습니다.")

        log_error(f"Successfully retrieved {len(columns)} columns from file: {filename}", level=20)  # INFO level

        return jsonify({
            "success": True,
            "columns": columns,
            "file_info": {
                "filename": filename,
                "row_count": len(data),
                "column_count": len(columns)
            }
        })

    except Exception as e:
        raise FileError(f"파일을 읽는 중 오류가 발생했습니다: {str(e)}", file_path=filepath)


@step2.route("/process", methods=["POST"])
@require_session
@handle_exceptions(include_traceback=True, log_errors=True)
def process():
    """Process an Excel file with NLP analysis and return results immediately"""
    # Get session ID
    session_id = get_or_create_session_id()

    # Validate session and get uploaded file
    filename = get_session_value(session_id, "uploaded_file")
    if not filename:
        raise SessionError("업로드된 파일이 없습니다.", session_id=session_id)

    # Validate required form data
    column_name = safe_get_form_data("column_name", required=True)

    # Get processing options with defaults
    language = safe_get_form_data("language", default="korean")
    analyzer = safe_get_form_data("analyzer", default="okt")

    # Validate language/analyzer combination
    if language.lower() == "english":
        spacy_status = get_spacy_status()
        if not spacy_status.get("installed", False):
            raise AnalysisError(
                "spaCy English model is not installed. Please contact server administrator.",
                analysis_type="spacy"
            )

    # Collect and validate options
    try:
        min_word_length = int(safe_get_form_data("min_word_length", default="2"))
        if min_word_length < 1:
            raise ValidationError("최소 단어 길이는 1 이상이어야 합니다.")
    except ValueError:
        raise ValidationError("최소 단어 길이는 숫자여야 합니다.")

    options = {
        "language": language,
        "analyzer": analyzer,
        "pos_tags": request.form.getlist("pos_tags"),
        "min_word_length": str(min_word_length),
        "custom_filename": safe_get_form_data("custom_filename", default=""),
        "original_filename": os.path.splitext(filename)[0],
        "column_name": column_name,
    }

    # Store options in session
    try:
        set_session_value(session_id, "options", options)
    except Exception as e:
        raise SessionError(f"세션 옵션 저장 중 오류가 발생했습니다: {str(e)}", session_id=session_id)

    # Create result folder if it doesn't exist
    RESULT_FOLDER = current_app.config["RESULT_FOLDER"]
    try:
        os.makedirs(RESULT_FOLDER, exist_ok=True)
    except OSError as e:
        raise FileError(f"결과 폴더 생성 중 오류가 발생했습니다: {str(e)}", file_path=RESULT_FOLDER)

    # Get and validate file path
    upload_folder = current_app.config["UPLOAD_FOLDER"]
    session_folder = os.path.join(upload_folder, session_id)
    filepath = os.path.join(session_folder, filename)

    validate_file_exists(filepath)

    # Process file
    try:
        log_error(f"Starting file processing for session {session_id}: {filename}", level=20)  # INFO level
        output_file, error = process_file(
            filepath, column_name, options=options, session_id=session_id
        )

        if error:
            raise AnalysisError(f"파일 처리 중 오류가 발생했습니다: {error}", analysis_type="nlp_processing")

        if not output_file:
            raise AnalysisError("파일 처리가 완료되었지만 출력 파일이 생성되지 않았습니다.", analysis_type="nlp_processing")

        # Build output filename
        pos_tags = options.get("pos_tags", [])
        if len(pos_tags) == 0:
            pos_tags_str = ""
        elif len(pos_tags) > 3:
            selected_tags = pos_tags[:3] + ["etc"]
            pos_tags_str = "_".join(selected_tags)
        else:
            pos_tags_str = "_".join(pos_tags)

        output_filename = f"{options['original_filename']}_{options['column_name']}_{options['analyzer']}_{pos_tags_str}_정제_ver1.xlsx"

        # Check if output file exists in the processed_files subdirectory
        result_folder = current_app.config["RESULT_FOLDER"]
        session_folder = os.path.join(result_folder, session_id)
        processed_folder = os.path.join(session_folder, current_app.config["PROCESSED_FILES_FOLDER"])
        file_path = os.path.join(processed_folder, output_filename)

        if not os.path.isfile(file_path) and output_file:
            # If the file path from process_file doesn't match our expected path
            # but process_file succeeded, use the returned path
            output_filename = os.path.basename(output_file)
            file_path = output_file

        # Validate output file exists
        if not os.path.isfile(file_path):
            raise FileError("처리 완료 후 출력 파일을 찾을 수 없습니다.", file_path=file_path)

        # Store processed file information in session
        try:
            set_session_value(session_id, "processed_file", output_filename)
        except Exception as e:
            raise SessionError(f"처리된 파일 정보 저장 중 오류가 발생했습니다: {str(e)}", session_id=session_id)

        log_error(f"File processing completed successfully for session {session_id}: {output_filename}", level=20)  # INFO level

        # Return the result with download URL
        return jsonify({
            "success": True,
            "session_id": session_id,
            "status": "completed",
            "download_url": url_for(
                "process.serve_file",
                filepath=f"{current_app.config['PROCESSED_FILES_FOLDER']}/{output_filename}",
                download="true",
                session_id=session_id,
            ),
            "filename": output_filename,
            "processing_options": options
        })

    except Exception as e:
        # Log the processing error
        log_error(f"File processing failed for session {session_id}: {str(e)}")
        raise AnalysisError(f"파일 처리 중 예상치 못한 오류가 발생했습니다: {str(e)}", analysis_type="nlp_processing")


@step2.route("/progress/<task_id>", methods=["GET"])
@require_session
def progress(task_id):
    """Get progress of a processing task"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Get or create session ID from the session or use the provided one
    if query_session_id:
        # Verify that the session exists
        session_data = get_session_by_id(query_session_id)
        if not session_data:
            return (
                jsonify({"error": f"Session with ID {query_session_id} not found"}),
                404,
            )
        session_id = query_session_id
    else:
        # Get or create session ID
        session_id = get_or_create_session_id()

    # Since processing is now synchronous, always return 100% progress
    # This endpoint is kept for backward compatibility
    return jsonify(
        {
            "task_id": task_id,
            "progress": 100,  # Always complete since processing is now synchronous
            "session_id": session_id,
        }
    )


@step2.route("/result/<task_id>", methods=["GET"])
@require_session
def get_result(task_id):
    """Get the result of a completed task"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Get or create session ID from the session or use the provided one
    if query_session_id:
        # Verify that the session exists
        session_data = get_session_by_id(query_session_id)
        if session_data:
            session_id = query_session_id
        else:
            return (
                jsonify({"error": f"Session with ID {query_session_id} not found"}),
                404,
            )
    else:
        # Get or create session ID
        session_id = get_or_create_session_id()

    # Get uploaded file information
    uploaded_file = get_session_value(session_id, "uploaded_file")
    if not uploaded_file:
        return jsonify({"error": "No uploaded file found in session"}), 404

    # Get options
    options = get_session_value(session_id, "options")
    if not options:
        return jsonify({"error": "No processing options found in session"}), 404

    # Build output filename
    filename = uploaded_file.split(".")[0]
    pos_tags = options.get("pos_tags", [])
    if len(pos_tags) == 0:
        pos_tags_str = ""
    elif len(options["pos_tags"]) > 3:
        selected_tags = options["pos_tags"][:3] + ["etc"]
        pos_tags_str = "_".join(selected_tags)
    else:
        pos_tags_str = "_".join(pos_tags)

    output_file = f"{filename}_{options['column_name']}_{options['analyzer']}_{pos_tags_str}_정제_ver1.xlsx"

    # Check if output file exists in the processed_files subdirectory
    result_folder = current_app.config["RESULT_FOLDER"]
    session_folder = os.path.join(result_folder, session_id)
    processed_folder = os.path.join(session_folder, current_app.config["PROCESSED_FILES_FOLDER"])
    file_path = os.path.join(processed_folder, output_file)

    if not os.path.isfile(file_path):
        return jsonify({"error": "Output file not found"}), 404

    # Return the result with download URL
    return jsonify(
        {
            "success": True,
            "task_id": task_id,
            "status": "completed",
            "download_url": url_for(
                "process.serve_file",
                filepath=f"{current_app.config['PROCESSED_FILES_FOLDER']}/{os.path.basename(output_file)}",
                download="true",
                session_id=session_id,
            ),
            "filename": os.path.basename(output_file),
        }
    )


@step2.route("/files/<path:filepath>", methods=["GET"])
@require_session
@handle_exceptions(include_traceback=True, log_errors=True)
def serve_file(filepath):
    """
    Unified endpoint to serve or download files from the results folder.

    Args:
        filepath: Path to the file relative to the session folder

    Query Parameters:
        download: Set to 'true' to download the file as an attachment
        session_id: Optional session ID to override the current session
    """
    # Validate filepath parameter
    if not filepath or filepath.strip() == "":
        raise ValidationError("파일 경로가 필요합니다.")

    # Security check: prevent directory traversal
    if ".." in filepath or filepath.startswith("/"):
        raise ValidationError("유효하지 않은 파일 경로입니다.")

    result_folder = current_app.config["RESULT_FOLDER"]

    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Get or create session ID from the session or use the provided one
    if query_session_id:
        # Verify that the session exists
        session_data = get_session_by_id(query_session_id)
        if not session_data:
            raise SessionError(f"Session with ID {query_session_id} not found", session_id=query_session_id)
        session_id = query_session_id
    else:
        # Get or create session ID
        session_id = get_or_create_session_id()

    # Ensure we have a valid session ID
    if not session_id:
        raise SessionError("유효한 세션 ID를 찾을 수 없습니다.")

    session_folder = os.path.join(result_folder, session_id)

    # Check if file exists in session folder
    file_path = os.path.join(session_folder, filepath)
    log_error(f"Looking for file at: {file_path}", level=20)  # INFO level

    if not os.path.isfile(file_path):
        log_error(f"File not found at primary location: {file_path}", level=30)  # WARNING level

        # Try to find the file in the common folder (without session)
        common_path = os.path.join(result_folder, filepath)
        if os.path.isfile(common_path):
            log_error(f"Found file in common folder: {common_path}", level=20)  # INFO level
            # If found in common folder, serve from there
            download = request.args.get("download", "false").lower() == "true"
            try:
                return send_from_directory(
                    result_folder,
                    filepath,
                    as_attachment=download,
                )
            except Exception as e:
                raise FileError(f"파일 전송 중 오류가 발생했습니다: {str(e)}", file_path=common_path)

        # Log session folder contents for debugging
        if os.path.exists(session_folder):
            try:
                contents = os.listdir(session_folder)
                log_error(f"Session folder contents: {contents}", level=20)  # INFO level
                # Also check subdirectories
                for item in contents:
                    item_path = os.path.join(session_folder, item)
                    if os.path.isdir(item_path):
                        subcontents = os.listdir(item_path)
                        log_error(f"Subdirectory {item} contents: {subcontents}", level=20)  # INFO level
            except OSError as e:
                log_error(f"Error listing session folder contents: {str(e)}")
        else:
            log_error(f"Session folder does not exist: {session_folder}")

        raise FileError(f"요청한 파일을 찾을 수 없습니다: {filepath}", file_path=file_path)

    # Determine if file should be downloaded as attachment
    download = request.args.get("download", "false").lower() == "true"

    # Serve the file
    try:
        log_error(f"Serving file: {file_path} (download={download})", level=20)  # INFO level
        return send_from_directory(session_folder, filepath, as_attachment=download)
    except Exception as e:
        raise FileError(f"파일 전송 중 오류가 발생했습니다: {str(e)}", file_path=file_path)


# Backward compatibility routes
@step2.route("/download/<filename>", methods=["GET"])
@require_session
def download(filename):
    """Redirect to the unified file endpoint with download=true"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Redirect to the files endpoint with download=true
    if query_session_id:
        return redirect(
            url_for(
                "process.serve_file",
                filepath=filename,
                download="true",
                session_id=query_session_id,
            )
        )
    else:
        return redirect(
            url_for("process.serve_file", filepath=filename, download="true")
        )


@step2.route("/results/<path:subpath>")
@require_session
def serve_results_file(subpath):
    """Redirect to the unified file endpoint"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Redirect to the files endpoint
    if query_session_id:
        return redirect(
            url_for("process.serve_file", filepath=subpath, session_id=query_session_id)
        )
    else:
        return redirect(url_for("process.serve_file", filepath=subpath))
