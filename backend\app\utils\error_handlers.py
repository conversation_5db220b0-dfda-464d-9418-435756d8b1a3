"""
Centralized error handling utilities for the Flask application.
Provides standardized error responses, logging, and common error types.
"""

import logging
import traceback
from functools import wraps
from typing import Any, Dict, Optional, Tuple, Union

from flask import current_app, jsonify, request


class AppError(Exception):
    """Base application error class."""
    
    def __init__(self, message: str, status_code: int = 500, details: Optional[Dict] = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.details = details or {}


class ValidationError(AppError):
    """Error for validation failures."""
    
    def __init__(self, message: str, field: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(message, 400, details)
        self.field = field


class FileError(AppError):
    """Error for file-related operations."""
    
    def __init__(self, message: str, file_path: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(message, 404, details)
        self.file_path = file_path


class SessionError(AppError):
    """Error for session-related operations."""
    
    def __init__(self, message: str, session_id: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(message, 400, details)
        self.session_id = session_id


class AnalysisError(AppError):
    """Error for analysis-related operations."""
    
    def __init__(self, message: str, analysis_type: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(message, 500, details)
        self.analysis_type = analysis_type


class ModelError(AppError):
    """Error for model-related operations."""
    
    def __init__(self, message: str, model_type: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(message, 500, details)
        self.model_type = model_type


def create_error_response(
    error: Union[str, Exception], 
    status_code: int = 500, 
    details: Optional[Dict] = None,
    include_traceback: bool = False
) -> Tuple[Dict[str, Any], int]:
    """
    Create a standardized error response.
    
    Args:
        error: Error message or exception
        status_code: HTTP status code
        details: Additional error details
        include_traceback: Whether to include traceback in response
        
    Returns:
        Tuple of (response_dict, status_code)
    """
    if isinstance(error, AppError):
        message = error.message
        status_code = error.status_code
        details = error.details
    elif isinstance(error, Exception):
        message = str(error)
    else:
        message = str(error)
    
    response = {
        "success": False,
        "error": message,
        "status_code": status_code
    }
    
    if details:
        response["details"] = details
    
    if include_traceback and isinstance(error, Exception):
        response["traceback"] = traceback.format_exc()
    
    # Add request context information
    if request:
        response["request_info"] = {
            "method": request.method,
            "endpoint": request.endpoint,
            "url": request.url
        }
    
    return response, status_code


def log_error(
    error: Union[str, Exception], 
    context: Optional[Dict] = None,
    level: int = logging.ERROR
) -> None:
    """
    Log an error with context information.
    
    Args:
        error: Error message or exception
        context: Additional context information
        level: Logging level
    """
    logger = current_app.logger if current_app else logging.getLogger(__name__)
    
    if isinstance(error, Exception):
        error_msg = f"{type(error).__name__}: {str(error)}"
        if level >= logging.ERROR:
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
        else:
            logger.log(level, error_msg)
    else:
        logger.log(level, str(error))
    
    if context:
        logger.log(level, f"Context: {context}")


def handle_exceptions(
    include_traceback: bool = False,
    log_errors: bool = True,
    default_status_code: int = 500
):
    """
    Decorator to handle exceptions in route functions.
    
    Args:
        include_traceback: Whether to include traceback in response
        log_errors: Whether to log errors
        default_status_code: Default status code for unhandled exceptions
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except AppError as e:
                if log_errors:
                    log_error(e, {"function": func.__name__})
                response, status_code = create_error_response(
                    e, include_traceback=include_traceback
                )
                return jsonify(response), status_code
            except Exception as e:
                if log_errors:
                    log_error(e, {"function": func.__name__})
                response, status_code = create_error_response(
                    e, default_status_code, include_traceback=include_traceback
                )
                return jsonify(response), status_code
        return wrapper
    return decorator


def validate_required_fields(data: Dict, required_fields: list) -> None:
    """
    Validate that required fields are present in data.
    
    Args:
        data: Data dictionary to validate
        required_fields: List of required field names
        
    Raises:
        ValidationError: If any required field is missing
    """
    missing_fields = [field for field in required_fields if not data.get(field)]
    if missing_fields:
        raise ValidationError(
            f"Missing required fields: {', '.join(missing_fields)}",
            details={"missing_fields": missing_fields}
        )


def validate_file_exists(file_path: str) -> None:
    """
    Validate that a file exists.
    
    Args:
        file_path: Path to the file
        
    Raises:
        FileError: If file doesn't exist
    """
    import os
    if not os.path.exists(file_path):
        raise FileError(f"File not found: {file_path}", file_path=file_path)


def validate_session_data(session_id: str, required_keys: list = None) -> Dict:
    """
    Validate session data and return it.
    
    Args:
        session_id: Session ID to validate
        required_keys: List of required keys in session data
        
    Returns:
        Session data dictionary
        
    Raises:
        SessionError: If session is invalid or missing required data
    """
    from ..services.session_service import get_session_by_id
    
    if not session_id:
        raise SessionError("Session ID is required")
    
    session_data = get_session_by_id(session_id)
    if not session_data:
        raise SessionError(f"Session not found: {session_id}", session_id=session_id)
    
    if required_keys:
        missing_keys = [key for key in required_keys if key not in session_data.get("data", {})]
        if missing_keys:
            raise SessionError(
                f"Missing required session data: {', '.join(missing_keys)}",
                session_id=session_id,
                details={"missing_keys": missing_keys}
            )
    
    return session_data


def safe_get_form_data(key: str, default: Any = None, required: bool = False) -> Any:
    """
    Safely get form data with validation.
    
    Args:
        key: Form field key
        default: Default value if key not found
        required: Whether the field is required
        
    Returns:
        Form field value
        
    Raises:
        ValidationError: If required field is missing
    """
    value = request.form.get(key, default)
    if required and not value:
        raise ValidationError(f"Required field '{key}' is missing", field=key)
    return value


def safe_get_json_data(key: str, default: Any = None, required: bool = False) -> Any:
    """
    Safely get JSON data with validation.
    
    Args:
        key: JSON field key
        default: Default value if key not found
        required: Whether the field is required
        
    Returns:
        JSON field value
        
    Raises:
        ValidationError: If required field is missing or JSON is invalid
    """
    try:
        json_data = request.get_json()
        if not json_data:
            if required:
                raise ValidationError("JSON data is required")
            return default
        
        value = json_data.get(key, default)
        if required and value is None:
            raise ValidationError(f"Required field '{key}' is missing", field=key)
        return value
    except Exception as e:
        raise ValidationError(f"Invalid JSON data: {str(e)}")
