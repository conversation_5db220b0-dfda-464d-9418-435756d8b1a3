from flask import request, jsonify, url_for
import traceback
import os

from ...services.json_session_service import get_session_value
from ...services.session_service import get_or_create_session_id
from ...services.nlp_service import (analyze_topics, get_file_path, read_file)
from ...utils.error_handlers import (
    AnalysisError, FileError, SessionError, ValidationError, handle_exceptions,
    log_error, safe_get_form_data, validate_file_exists
)
from . import bert_bp


# Configure CORS for React frontend with HTTPS support
@bert_bp.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    # Set UTF-8 charset for JSON responses to properly handle Korean characters
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'

    return response

@bert_bp.route('/analyze', methods=['POST'])
@handle_exceptions(include_traceback=True, log_errors=True)
def analyze():
    session_id = get_or_create_session_id()
    if not session_id:
        raise SessionError("유효한 세션 ID를 생성할 수 없습니다.")

    # 필수 파라미터 확인
    column_name = safe_get_form_data('column_name', required=True)

    # 모델 파라미터 가져오기 및 검증
    nr_topics = safe_get_form_data('nr_topics', default="auto")
    top_n_words_str = safe_get_form_data('top_n_words', default="10")
    min_topic_size_str = safe_get_form_data('min_topic_size', default="5")
    max_ngram_str = safe_get_form_data('max_ngram', default="1")
    min_df_str = safe_get_form_data('min_df', default="2")
    embedding_model = safe_get_form_data('embedding_model',
                          default="sentence-transformers/xlm-r-100langs-bert-base-nli-stsb-mean-tokens")

    # 숫자 파라미터 검증
    try:
        if nr_topics != "auto":
            nr_topics_int = int(nr_topics)
            if nr_topics_int < 2 or nr_topics_int > 50:
                raise ValidationError("토픽 수는 2-50 사이여야 합니다.")

        top_n_words = int(top_n_words_str)
        if top_n_words < 5 or top_n_words > 50:
            raise ValidationError("상위 단어 수는 5-50 사이여야 합니다.")

        min_topic_size = int(min_topic_size_str)
        if min_topic_size < 2 or min_topic_size > 100:
            raise ValidationError("최소 토픽 크기는 2-100 사이여야 합니다.")

        max_ngram = int(max_ngram_str)
        if max_ngram < 1 or max_ngram > 3:
            raise ValidationError("최대 N-gram은 1-3 사이여야 합니다.")

        min_df = int(min_df_str)
        if min_df < 1 or min_df > 100:
            raise ValidationError("최소 문서 빈도는 1-100 사이여야 합니다.")

    except ValueError as e:
        raise ValidationError(f"파라미터 값이 올바르지 않습니다: {str(e)}")

    params = {
        'nr_topics': nr_topics,
        'top_n_words': top_n_words_str,
        'min_topic_size': min_topic_size_str,
        'max_ngram': max_ngram_str,
        'min_df': min_df_str,
        'embedding_model': embedding_model
    }

    # 파일 정보 가져오기
    filename = get_session_value(session_id, "uploaded_file")
    if not filename:
        raise SessionError("업로드된 파일이 없습니다.", session_id=session_id)

    file_path = get_file_path(session_id, filename, "uploaded")
    if not file_path:
        raise FileError("분석할 파일을 찾을 수 없습니다.")

    # 파일 존재 확인
    validate_file_exists(file_path)

    try:
        # 파일 읽기
        df = read_file(file_path)
        if df.empty:
            raise ValidationError("파일이 비어있습니다.")

    except Exception as e:
        if isinstance(e, ValidationError):
            raise
        raise FileError(f'파일 읽기 실패: {str(e)}', file_path=file_path)

    # 컬럼 존재 확인
    if column_name not in df.columns:
        raise ValidationError(f'선택한 컬럼 "{column_name}"이 존재하지 않습니다.')

    # 데이터 검증
    if df[column_name].isnull().all() or df[column_name].astype(str).str.strip().eq("").all():
        raise ValidationError('선택한 컬럼에 분석 가능한 텍스트 데이터가 없습니다.')

    try:
        # 분석 실행
        log_error(f"Starting BERT analysis for session {session_id}: {filename}", level=20)  # INFO level
        result = analyze_topics(df, column_name, params)

        if not result:
            raise AnalysisError("BERT 분석 결과가 비어있습니다.", analysis_type="bert")

        if "error" in result:
            raise AnalysisError(f"BERT 분석 중 오류가 발생했습니다: {result['error']}", analysis_type="bert")

    except ValueError as ve:
        raise AnalysisError(f"BERT 분석 파라미터 오류: {str(ve)}", analysis_type="bert")
    except Exception as e:
        if isinstance(e, (AnalysisError, ValidationError, FileError, SessionError)):
            raise
        error_details = str(e)
        if "list index out of range" in error_details:
            raise AnalysisError('토픽 추출에 실패했습니다. 데이터 크기가 너무 작거나 텍스트 내용이 부적절할 수 있습니다.', analysis_type="bert")
        raise AnalysisError(f"BERT 분석 중 예상치 못한 오류가 발생했습니다: {error_details}", analysis_type="bert")

    # 결과에 다운로드 URL 추가
    if 'file_path' in result:
        result["excel_download_url"] = url_for(
            "process.serve_file",
            filepath=result["file_path"],
            download="true",
            session_id=session_id,
            _external=True,
        )

    log_error(f"BERT analysis completed successfully for session {session_id}", level=20)  # INFO level
    return jsonify({
        "success": True,
        "session_id": session_id,
        **result
    })

# @bert_bp.route('/download/<path:filename>', methods=['GET'])
# def download_file(filename):
#     directory = os.path.join(app.root_path, 'static', 'results')
#     return send_from_directory(directory, filename, as_attachment=True)

# if __name__ == '__main__':
#     app.run(debug=False, threaded=True, host='0.0.0.0', port=5004) 