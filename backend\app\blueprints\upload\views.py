import os

import pandas as pd
from flask import current_app, jsonify, request, send_from_directory
from werkzeug.utils import secure_filename

from ...services.file_processor import read_file
from ...services.json_session_service import (get_session_value,
                                              set_session_value)
from ...services.session_service import (get_or_create_session_id,
                                         require_session)
from ...utils.error_handlers import (
    FileError, SessionError, ValidationError, handle_exceptions,
    log_error, safe_get_form_data, validate_file_exists, validate_required_fields
)
from . import upload_bp

# configuration constants from app.config via current_app.config
ALLOWED_EXTENSIONS = {"xlsx", "csv"}


# Configure CORS for React frontend with HTTPS support
@upload_bp.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    # Set UTF-8 charset for JSON responses to properly handle Korean characters
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'

    return response


def allowed_file(filename):
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


@upload_bp.route("/upload", methods=["POST"])
@require_session
@handle_exceptions(include_traceback=True, log_errors=True)
def upload_file():
    """
    Upload a file and associate it with the current session.
    Creates a new session if one doesn't exist.
    """
    # Get or create session ID
    session_id = get_or_create_session_id()

    # Validate file upload
    if "file" not in request.files:
        raise ValidationError("파일이 전송되지 않았습니다.")

    file = request.files["file"]
    if not file or file.filename == "":
        raise ValidationError("파일이 선택되지 않았습니다.")

    # Validate file type
    if not allowed_file(file.filename):
        raise ValidationError(
            "허용되지 않는 파일 형식입니다.",
            details={"allowed_extensions": list(ALLOWED_EXTENSIONS)}
        )

    # Secure the filename
    filename = secure_filename(file.filename)
    if not filename:
        raise ValidationError("유효하지 않은 파일명입니다.")

    # Prepare file paths
    upload_folder = current_app.config["UPLOAD_FOLDER"]
    session_folder = os.path.join(upload_folder, session_id)
    filepath = os.path.join(session_folder, filename)

    try:
        # Ensure session folder exists
        os.makedirs(session_folder, exist_ok=True)

        # Save the file
        file.save(filepath)
        log_error(f"File uploaded successfully: {filepath}", level=20)  # INFO level

    except OSError as e:
        raise FileError(f"파일 저장 중 오류가 발생했습니다: {str(e)}", file_path=filepath)
    except Exception as e:
        raise FileError(f"파일 업로드 중 예상치 못한 오류가 발생했습니다: {str(e)}", file_path=filepath)

    try:
        # Read and validate file content
        df = read_file(filepath)

        if df.empty:
            # Clean up empty file
            if os.path.exists(filepath):
                os.remove(filepath)
            raise ValidationError("업로드된 파일이 비어있습니다.")

        columns = df.columns.tolist()
        if not columns:
            # Clean up file with no columns
            if os.path.exists(filepath):
                os.remove(filepath)
            raise ValidationError("파일에 유효한 컬럼이 없습니다.")

    except pd.errors.EmptyDataError:
        # Clean up invalid file
        if os.path.exists(filepath):
            os.remove(filepath)
        raise ValidationError("파일이 비어있거나 읽을 수 없습니다.")
    except pd.errors.ParserError as e:
        # Clean up unparseable file
        if os.path.exists(filepath):
            os.remove(filepath)
        raise ValidationError(f"파일 형식이 올바르지 않습니다: {str(e)}")
    except Exception as e:
        # Clean up on any file reading error
        if os.path.exists(filepath):
            os.remove(filepath)
        raise FileError(f"파일을 읽는 중 오류가 발생했습니다: {str(e)}", file_path=filepath)

    try:
        # Store filename in session data
        set_session_value(session_id, "uploaded_file", filename)

    except Exception as e:
        # Clean up file if session storage fails
        if os.path.exists(filepath):
            os.remove(filepath)
        raise SessionError(f"세션 데이터 저장 중 오류가 발생했습니다: {str(e)}", session_id=session_id)

    return jsonify({
        "success": True,
        "filename": filename,
        "columns": columns,
        "session_id": session_id,
        "file_size": len(df),
        "column_count": len(columns)
    })


@upload_bp.route("/select-columns", methods=["POST"])
@require_session
@handle_exceptions(include_traceback=True, log_errors=True)
def select_columns():
    """
    Select columns from the uploaded file for processing.
    Requires an active session with an uploaded file.
    """
    # Ensure we have an active session
    session_id = get_or_create_session_id()

    # Get uploaded file from session data
    uploaded_file = get_session_value(session_id, "uploaded_file")
    if not uploaded_file:
        raise SessionError("업로드된 파일이 없습니다.", session_id=session_id)

    # Get selected columns from request
    session_data = get_session_value(session_id, "data")
    if session_data:
        data = pd.DataFrame.from_dict(session_data)
        selected_columns = data.get("columns", [])
    else:
        if request.is_json:
            selected_columns = request.json.get("columns", [])
        else:
            selected_columns = request.form.getlist("columns")

    # Validate selected columns
    if not selected_columns:
        raise ValidationError("선택된 컬럼이 필요합니다.")

    # Prepare file path
    upload_folder = current_app.config["UPLOAD_FOLDER"]
    session_folder = os.path.join(upload_folder, session_id)
    filepath = os.path.join(session_folder, uploaded_file)

    # Validate file exists
    validate_file_exists(filepath)

    try:
        # Read file based on extension
        if uploaded_file.endswith(".csv"):
            df = pd.read_csv(filepath)
        else:
            df = pd.read_excel(filepath)

    except pd.errors.EmptyDataError:
        raise ValidationError("파일이 비어있습니다.")
    except pd.errors.ParserError as e:
        raise ValidationError(f"파일을 파싱할 수 없습니다: {str(e)}")
    except Exception as e:
        raise FileError(f"파일을 읽는 중 오류가 발생했습니다: {str(e)}", file_path=filepath)

    # Validate selected columns exist in the file
    missing_columns = [col for col in selected_columns if col not in df.columns]
    if missing_columns:
        raise ValidationError(
            f"선택된 컬럼이 파일에 존재하지 않습니다: {', '.join(missing_columns)}",
            details={
                "missing_columns": missing_columns,
                "available_columns": df.columns.tolist()
            }
        )

    try:
        # Combine selected columns into text
        combined_text = df[selected_columns].astype(str).agg(" ".join, axis=1)
        result_df = pd.DataFrame({"Combined_Text": combined_text})

        # Store data in session
        set_session_value(session_id, "columns", selected_columns)
        set_session_value(session_id, "data", result_df.to_dict())

        data_info = {
            "row_count": len(result_df),
            "column_count": len(selected_columns),
            "original_columns": selected_columns,
            "preview": result_df.head(10).to_dict("records"),
            "session_id": session_id
        }

        return jsonify({"success": True, "data_info": data_info})

    except Exception as e:
        raise ValidationError(f"데이터 처리 중 오류가 발생했습니다: {str(e)}")
