import json
import os

import matplotlib
from flask import current_app, jsonify, request, url_for


def get_request_data(key, default=None):
    """Helper function to get data from either JSON or form data"""
    if request.is_json and request.get_json():
        return request.get_json().get(key, default)
    else:
        return request.form.get(key, default)

from ...services.json_session_service import get_session_value
from ...services.nlp_service import (create_topic_network,
                                     create_updated_pyldavis, get_file_path,
                                     get_lda_model, get_updated_topics,
                                     run_lda_analysis,
                                     run_lda_analysis_with_edits,
                                     update_topic_visualizations)
from ...services.session_service import get_or_create_session_id
from ...utils.error_handlers import (
    AnalysisError, FileError, ModelError, SessionError, ValidationError,
    handle_exceptions, log_error, safe_get_form_data, validate_file_exists
)
from . import lda_bp

matplotlib.use("Agg")
import matplotlib.pyplot as plt

# Global variables for LDA model state
global_model = None
global_corpus = None
global_dictionary = None
global_tokens = None
global_edited_keywords = {}

# Configure CORS for React frontend with HTTPS support
@lda_bp.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    # Set UTF-8 charset for JSON responses to properly handle Korean characters
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'

    return response

# (4-3) 데이터 처리 및 LDA 분석 수행
@lda_bp.route("/process", methods=["POST", "OPTIONS"])
@handle_exceptions(include_traceback=True, log_errors=True)
def process_data():
    """LDA 분석 처리"""
    if request.method == "OPTIONS":
        return "", 200

    # (4-3-1) 업로드된 파일 존재 확인
    session_id = request.args.get("session_id")
    if not session_id:
        session_id = get_or_create_session_id()

    if not session_id:
        raise SessionError("유효한 세션 ID를 생성할 수 없습니다.")

    # Check for processed file first, then fall back to uploaded file
    processed_filename = None
    options = get_session_value(session_id, "options")
    if options:
        # Try to construct the processed filename
        original_filename = options.get("original_filename", "")
        column_name = options.get("column_name", "")
        analyzer = options.get("analyzer", "okt")
        pos_tags = options.get("pos_tags", [])

        if len(pos_tags) == 0:
            pos_tags_str = ""
        elif len(pos_tags) > 3:
            selected_tags = pos_tags[:3] + ["etc"]
            pos_tags_str = "_".join(selected_tags)
        else:
            pos_tags_str = "_".join(pos_tags)

        processed_filename = f"{original_filename}_{column_name}_{analyzer}_{pos_tags_str}_정제_ver1.xlsx"

    # Check if processed file exists in the processed_files subdirectory
    if processed_filename:
        result_folder = current_app.config["RESULT_FOLDER"]
        session_folder = os.path.join(result_folder, session_id)
        processed_folder = os.path.join(session_folder, current_app.config["PROCESSED_FILES_FOLDER"])
        processed_file_path = os.path.join(processed_folder, processed_filename)

        if os.path.exists(processed_file_path):
            filename = processed_filename
            file_path = processed_file_path
        else:
            # Fall back to uploaded file
            filename = get_session_value(session_id, "uploaded_file")
            if not filename:
                raise FileError("처리된 파일과 업로드된 파일을 모두 찾을 수 없습니다")
            file_path = get_file_path(session_id, filename, "uploaded")
    else:
        # No processed file info, use uploaded file
        filename = get_session_value(session_id, "uploaded_file")
        if not filename:
            return jsonify({"error": "업로드된 파일이 없습니다"})
        file_path = get_file_path(session_id, filename, "uploaded")

    # (4-3-2) 분석 파라미터 가져오기 및 검증 - JSON과 form data 모두 지원
    text_column = get_request_data("text_column") or get_request_data("column_name")  # 텍스트 데이터가 있는 컬럼명
    if not text_column:
        raise ValidationError("분석할 텍스트 컬럼이 필요합니다.")

    # 토픽 개수 파라미터 검증
    try:
        min_topic = int(get_request_data("min_topic", 3))  # 최소 토픽 개수
        max_topic = int(get_request_data("max_topic", 10))  # 최대 토픽 개수

        if min_topic < 2:
            raise ValidationError("최소 토픽 개수는 2 이상이어야 합니다.")
        if max_topic < min_topic:
            raise ValidationError("최대 토픽 개수는 최소 토픽 개수보다 크거나 같아야 합니다.")
        if max_topic > 50:
            raise ValidationError("최대 토픽 개수는 50 이하여야 합니다.")

    except ValueError as e:
        raise ValidationError(f"토픽 개수는 숫자여야 합니다: {str(e)}")

    # 문서 빈도 파라미터 검증
    try:
        no_below = int(get_request_data("no_below", 5))  # 최소 문서 빈도
        no_above = float(get_request_data("no_above", 0.2))  # 최대 문서 빈도

        if no_below < 1:
            raise ValidationError("최소 문서 빈도는 1 이상이어야 합니다.")
        if no_above <= 0 or no_above >= 1:
            raise ValidationError("최대 문서 빈도는 0과 1 사이의 값이어야 합니다.")

    except ValueError as e:
        raise ValidationError(f"문서 빈도 파라미터가 올바르지 않습니다: {str(e)}")

    # (4-3-3) 네트워크 시각화 스타일 설정
    network_style = get_request_data("network_style", "academic")  # 기본값을 논문용 스타일로 변경
    chart_style = get_request_data("chart_style", "default")  # 기본값은 default 스타일

    # (4-3-4) 수동 토픽 수 설정 확인
    manual_topic_number = get_request_data("manual_topic_number")
    if manual_topic_number:
        try:
            manual_topic_number = int(manual_topic_number)
            if manual_topic_number < 2 or manual_topic_number > 50:
                raise ValidationError("수동 토픽 개수는 2-50 사이여야 합니다.")
        except ValueError:
            raise ValidationError("수동 토픽 개수는 숫자여야 합니다.")

    # (4-3-5) 파일 존재 확인 (file_path is already determined above)
    validate_file_exists(file_path)

    # (4-3-6) 편집된 키워드 초기화 (새로운 모델 학습 시)
    global global_edited_keywords
    global_edited_keywords = {}

    # (4-3-7) LDA 분석 실행 - 전체 모드가 기본값
    # 빠른 모드 설정 (기본값: False - 전체 모드)
    fast_mode_value = get_request_data("fast_mode", False)
    if isinstance(fast_mode_value, str):
        fast_mode = fast_mode_value.lower() == "true"
    else:
        fast_mode = bool(fast_mode_value)

    try:
        log_error(f"Starting LDA analysis for session {session_id} with parameters: text_column={text_column}, min_topic={min_topic}, max_topic={max_topic}", level=20)  # INFO level

        results = run_lda_analysis(
            file_path,
            text_column,
            min_topic,
            max_topic,
            no_below,
            no_above,
            network_style,
            manual_topic_number,
            chart_style,  # 차트 스타일 매개변수 추가
            fast_mode,  # 빠른 모드 매개변수 추가
        )

        if not results:
            raise AnalysisError("LDA 분석 결과가 비어있습니다.", analysis_type="lda")

        if "error" in results:
            raise AnalysisError(f"LDA 분석 중 오류가 발생했습니다: {results['error']}", analysis_type="lda")

        log_error(f"LDA analysis completed successfully for session {session_id}", level=20)  # INFO level

    except Exception as e:
        if isinstance(e, (AnalysisError, ValidationError, FileError, SessionError)):
            raise
        raise AnalysisError(f"LDA 분석 중 예상치 못한 오류가 발생했습니다: {str(e)}", analysis_type="lda")

    # Add full download URLs to the results
    if "csv_path" in results:
        results["csv_download_url"] = url_for(
            "process.serve_file",
            filepath=results["csv_path"],
            download="true",
            session_id=session_id,
            _external=True,
        )

    if "network_img_path" in results:
        results["network_img_url"] = url_for(
            "process.serve_file",
            filepath=results["network_img_path"],
            download="false",
            session_id=session_id,
            _external=True,
        )

    # Add URLs for topic images
    if "topic_images" in results and isinstance(results["topic_images"], list):
        for topic_image in results["topic_images"]:
            if "path" in topic_image:
                topic_image["url"] = url_for(
                    "process.serve_file",
                    filepath=topic_image["path"],
                    download="false",
                    session_id=session_id,
                    _external=True,
                )

    return jsonify(results)


# (4-4) 키워드 편집 처리
@lda_bp.route("/edit_keywords", methods=["POST", "OPTIONS"])
@handle_exceptions(include_traceback=True, log_errors=True)
def edit_keywords():
    """키워드 편집 처리"""
    if request.method == "OPTIONS":
        return "", 200
    global global_model, global_corpus, global_dictionary, global_tokens, global_edited_keywords

    # (4-4-1) 모델 로드 확인 - get_lda_model 함수 사용
    session_id = request.args.get("session_id")
    if not session_id:
        raise SessionError("세션 ID가 필요합니다.")

    try:
        global_model, global_corpus, global_dictionary = get_lda_model(session_id)
    except Exception as e:
        raise ModelError(f"모델 로드 중 오류가 발생했습니다: {str(e)}", model_type="lda")

    if global_model is None:
        raise ModelError("모델이 로드되지 않았습니다. 먼저 데이터를 처리해주세요.", model_type="lda")

    # (4-4-2) 편집 정보 가져오기 - form data로 변경
    topic_id = int(request.form.get("topic_id"))
    # JSON 문자열로 전송된 데이터를 파싱
    edited_words = json.loads(request.form.get("edited_words", "[]")) # pass as list of dictionaries
    removed_words = json.loads(request.form.get("removed_words", "[]")) # list of strings
    chart_style = request.form.get("chart_style", "default")

    # (4-4-3) 편집된 키워드 정보 저장 구조 초기화
    if topic_id not in global_edited_keywords:
        global_edited_keywords[topic_id] = {"edited": {}, "removed": []}

    # (4-4-4) 편집된 단어 정보 업데이트
    for word_info in edited_words:
        original_word = word_info.get("original")
        new_word = word_info.get("new")
        if original_word and new_word and original_word.lower() != new_word.lower():
            global_edited_keywords[topic_id]["edited"][
                original_word.lower()
            ] = new_word.lower()

    # (4-4-5) 제거된 단어 정보 업데이트 - 수정된 로직
    for word in removed_words:
        word_lower = word.lower()
        # 제거 목록에 추가
        if word_lower not in global_edited_keywords[topic_id]["removed"]:
            global_edited_keywords[topic_id]["removed"].append(word_lower)

        # 만약 이 단어가 이전에 편집되었다면, 편집 정보도 제거
        # 편집된 단어들 중에서 이 단어와 일치하는 것을 찾아서 제거
        edited_dict = global_edited_keywords[topic_id]["edited"]
        keys_to_remove = []
        for original_key, edited_value in edited_dict.items():
            # 원본 단어나 편집된 단어가 제거 대상이면 편집 정보 삭제
            if original_key == word_lower or edited_value == word_lower:
                keys_to_remove.append(original_key)

        for key in keys_to_remove:
            del edited_dict[key]

    # (4-4-6) Call run_lda_analysis with skip_training=True to get the same structure as process endpoint
    try:
        # Get the file path for reanalysis (needed for some visualization functions)
        filename = get_session_value(session_id, "uploaded_file")
        if not filename:
            return jsonify({"error": "업로드된 파일이 없습니다"})

        file_path = get_file_path(session_id, filename, "uploaded")
        if not os.path.exists(file_path):
            return jsonify({"error": "파일을 찾을 수 없습니다."}), 404

        # Call run_lda_analysis but skip training and use existing model with edited keywords
        network_style = request.form.get("network_style", "academic")
        result = run_lda_analysis_with_edits(
            file_path=file_path,
            existing_model=global_model,
            existing_corpus=global_corpus,
            existing_dictionary=global_dictionary,
            edited_keywords=global_edited_keywords,
            network_style=network_style,
            chart_style=chart_style,
            session_id=session_id,
        )

        # Add download URLs (these are already included in the result from run_lda_analysis_with_edits)
        if "csv_path" in result:
            result["csv_download_url"] = url_for(
                "process.serve_file",
                filepath=result["csv_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        if "network_img_path" in result:
            result["network_img_url"] = url_for(
                "process.serve_file",
                filepath=result["network_img_path"],
                download="false",
                session_id=session_id,
                _external=True,
            )

        # Add URLs for topic images
        if "topic_images" in result and isinstance(result["topic_images"], list):
            for topic_image in result["topic_images"]:
                if "path" in topic_image:
                    topic_image["url"] = url_for(
                        "process.serve_file",
                        filepath=topic_image["path"],
                        download="false",
                        session_id=session_id,
                        _external=True,
                    )

        # (4-4-7) 편집 결과 반환
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)})


# 차트 스타일 변경 엔드포인트 추가
@lda_bp.route("/update_chart_style", methods=["POST", "OPTIONS"])
def update_chart_style():
    """차트 스타일 변경"""
    if request.method == "OPTIONS":
        return "", 200
    global global_model, global_edited_keywords

    # 새로운 get_lda_model 함수를 사용하여 모델 가져오기
    session_id = request.args.get("session_id")
    global_model, _, _ = get_lda_model(session_id)

    # 모델이 없으면 오류 반환
    if global_model is None:
        return jsonify(
            {"error": "모델이 로드되지 않았습니다. 먼저 데이터를 처리해주세요."}
        )

    chart_style = request.form.get("chart_style", "default")

    try:
        # 토픽별 시각화 이미지 업데이트
        topic_images = update_topic_visualizations(
            global_model, global_edited_keywords, chart_style
        )

        # Get session ID for URLs
        session_id = request.args.get("session_id")
        if not session_id:
            session_id = get_or_create_session_id()

        # Add URLs for topic images
        if topic_images and isinstance(topic_images, list):
            for topic_image in topic_images:
                if "path" in topic_image:
                    topic_image["url"] = url_for(
                        "process.serve_file",
                        filepath=topic_image["path"],
                        download="false",
                        session_id=session_id,
                        _external=True,
                    )

        return jsonify(
            {"success": True, "topic_images": topic_images, "chart_style": chart_style}
        )
    except Exception as e:
        # 오류 발생 시 기본 이미지 생성
        result_folder = current_app.config["RESULT_FOLDER"]
        session_id = request.args.get("session_id")
        if not session_id:
            session_id = get_or_create_session_id()
        session_folder = os.path.join(result_folder, session_id)
        topic_folder = os.path.join(
            session_folder, current_app.config["TOPIC_MODELS_FOLDER"]
        )

        try:
            used_topic_num = global_model.num_topics if global_model else 3
            topic_images = []

            for topic_id in range(used_topic_num):
                topic_img_path = os.path.join(
                    topic_folder, f"topic_{topic_id + 1}_default.png"
                )

                # 기본 이미지 파일이 없는 경우 생성
                if not os.path.exists(topic_img_path):
                    try:
                        # 이미지 저장을 위한 디렉토리 확인
                        os.makedirs(os.path.dirname(topic_img_path), exist_ok=True)

                        # 매우 간단한 막대 그래프 생성
                        plt.figure(figsize=(10, 6))
                        plt.barh(
                            [f"단어 {i + 1}" for i in range(5)],
                            [5 - i for i in range(5)],
                        )
                        plt.title(f"TOPIC {topic_id + 1} (기본 이미지)")
                        plt.xlabel("가중치")
                        plt.ylabel("키워드")
                        plt.savefig(topic_img_path)
                        plt.close()
                    except Exception as e2:
                        print(f"기본 토픽 이미지 생성 실패: {str(e2)}")

                # Convert to relative path for serving
                relative_path = os.path.join(
                    "lda_analysis",
                    current_app.config["TOPIC_MODELS_FOLDER"],
                    os.path.basename(topic_img_path),
                )
                topic_images.append({"id": topic_id, "path": relative_path})

            # Add URLs for topic images
            if topic_images and isinstance(topic_images, list):
                for topic_image in topic_images:
                    if "path" in topic_image:
                        topic_image["url"] = url_for(
                            "process.serve_file",
                            filepath=topic_image["path"],
                            download="false",
                            session_id=session_id,
                            _external=True,
                        )

            return jsonify(
                {
                    "success": True,
                    "topic_images": topic_images,
                    "chart_style": chart_style,
                    "warning": "차트 스타일 변경 중 오류가 발생하여 기본 이미지가 생성되었습니다.",
                }
            )
        except Exception as recovery_error:
            return jsonify(
                {
                    "error": f"차트 스타일 변경 및 복구 시도 실패: {str(e)}, 복구 오류: {str(recovery_error)}"
                }
            )


@lda_bp.route("/update_topics", methods=["POST", "OPTIONS"])
def update_topics():
    """토픽 업데이트"""
    if request.method == "OPTIONS":
        return "", 200
    # Get current session ID
    session_id = request.args.get("session_id")
    if not session_id:
        session_id = get_or_create_session_id()

    # Get edited keywords from request - form data로 변경
    edited_keywords = json.loads(request.form.get("edited_keywords", "{}"))

    # Get current model
    global global_model, global_corpus, global_dictionary
    global_model, global_corpus, global_dictionary = get_lda_model()

    if global_model is None:
        return jsonify({"error": "No LDA model found"})

    try:
        # Update topics with edited keywords
        topics = get_updated_topics(global_model, edited_keywords)

        # Create updated visualizations
        vis_html = create_updated_pyldavis(
            global_model, global_corpus, global_dictionary, edited_keywords
        )
        topic_images = update_topic_visualizations(global_model, edited_keywords)

        return jsonify(
            {"topics": topics, "pyldavis_html": vis_html, "topic_images": topic_images}
        )
    except Exception as e:
        return jsonify({"error": str(e)})


@lda_bp.route("/update_network", methods=["POST", "OPTIONS"])
def update_network():
    """네트워크 업데이트"""
    if request.method == "OPTIONS":
        return "", 200
    # Get current session ID
    session_id = request.args.get("session_id")
    if not session_id:
        session_id = get_or_create_session_id()

    # Get edited keywords and style from request - form data로 변경
    edited_keywords = json.loads(request.form.get("edited_keywords", "{}"))
    network_style = request.form.get("network_style", "academic")

    # Get current model
    global global_model
    global_model, _, _ = (
        get_lda_model()
    )  # We only need the model for network visualization

    if global_model is None:
        return jsonify({"error": "No LDA model found"})

    try:
        # Create updated network visualization
        network_img_path = create_topic_network(
            global_model,
            global_model.num_topics,
            network_style=network_style,
            edited_keywords=edited_keywords,
        )

        # Add download URL to the response
        network_img_url = url_for(
            "process.serve_file",
            filepath=network_img_path,
            download="false",
            session_id=session_id,
            _external=True,
        )
        
        return jsonify(
            {"network_img_path": network_img_path, "network_img_url": network_img_url}
        )
    except Exception as e:
        return jsonify({"error": str(e)})
