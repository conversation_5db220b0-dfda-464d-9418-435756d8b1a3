# app/blueprints/analysis/tfidf_routes.py
import json
import logging
import os
import traceback
from typing import Dict, Any, <PERSON><PERSON>

from flask import Blueprint, jsonify, request, send_file, url_for, current_app
from werkzeug.utils import secure_filename
from werkzeug.exceptions import BadRequest, RequestEntityTooLarge

from ...services.json_session_service import (get_session_value,
                                              set_session_value)
from ...services.nlp_service import (TFIDFAnalyzer, analyze_frequency, WordCloudGenerator,
                                     create_topic_network,
                                     create_updated_pyldavis, get_file_path,
                                     get_lda_model, get_updated_topics,
                                     read_file, run_lda_analysis,
                                     run_lda_analysis_with_edits,
                                     update_topic_visualizations)
from ...services.session_service import get_or_create_session_id
from . import tfidf_bp

# Setup logging
logger = logging.getLogger(__name__)

# Error handling utilities
def validate_request_data(required_fields: list, form_data: dict) -> Tuple[bool, str]:
    """
    Validate required fields in request data

    Args:
        required_fields: List of required field names
        form_data: Form data dictionary

    Returns:
        Tuple of (is_valid, error_message)
    """
    for field in required_fields:
        if not form_data.get(field):
            return False, f"필수 필드가 누락되었습니다: {field}"
    return True, ""

def validate_json_data(json_string: str, field_name: str) -> Tuple[bool, Any, str]:
    """
    Validate and parse JSON data

    Args:
        json_string: JSON string to parse
        field_name: Name of the field for error messages

    Returns:
        Tuple of (is_valid, parsed_data, error_message)
    """
    try:
        if not json_string:
            return False, None, f"{field_name}이 비어있습니다"

        parsed_data = json.loads(json_string)
        return True, parsed_data, ""
    except json.JSONDecodeError as e:
        return False, None, f"{field_name} JSON 파싱 오류: {str(e)}"
    except Exception as e:
        return False, None, f"{field_name} 처리 오류: {str(e)}"

def create_error_response(error_message: str, status_code: int = 400, include_traceback: bool = False) -> Tuple[Dict[str, Any], int]:
    """
    Create standardized error response

    Args:
        error_message: Error message to return
        status_code: HTTP status code
        include_traceback: Whether to include traceback in response

    Returns:
        Tuple of (response_dict, status_code)
    """
    response = {
        "success": False,
        "error": error_message
    }

    if include_traceback and current_app.debug:
        response["traceback"] = traceback.format_exc()

    return response, status_code

def handle_file_validation(session_id: str, filename: str = None) -> Tuple[bool, str, str]:
    """
    Validate file existence and accessibility

    Args:
        session_id: Session ID
        filename: Optional filename override

    Returns:
        Tuple of (is_valid, file_path, error_message)
    """
    try:
        if not filename:
            filename = get_session_value(session_id, "uploaded_file")

        if not filename:
            return False, "", "업로드된 파일이 없습니다. 먼저 파일을 업로드해주세요."

        file_path = get_file_path(session_id, filename, "uploaded")

        if not os.path.exists(file_path):
            return False, "", f"파일을 찾을 수 없습니다: {filename}"

        if not os.access(file_path, os.R_OK):
            return False, "", f"파일에 접근할 수 없습니다: {filename}"

        return True, file_path, ""
    except Exception as e:
        logger.error(f"파일 검증 오류: {traceback.format_exc()}")
        return False, "", f"파일 검증 중 오류가 발생했습니다: {str(e)}"


# Configure CORS for React frontend with HTTPS support
@tfidf_bp.after_request
def after_request(response):
    """Configure CORS headers with error handling"""
    try:
        # Get CORS allowed origins from environment variable or use a default
        cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

        # If specific origins are provided, use them
        if cors_allowed_origins != "*":
            # Check if the request origin is in the allowed origins
            request_origin = request.headers.get("Origin")
            if request_origin:
                try:
                    if "," in cors_allowed_origins:
                        allowed_origins = [origin.strip() for origin in cors_allowed_origins.split(",")]
                        if request_origin in allowed_origins:
                            response.headers.add("Access-Control-Allow-Origin", request_origin)
                    else:
                        if request_origin == cors_allowed_origins.strip():
                            response.headers.add("Access-Control-Allow-Origin", request_origin)
                except Exception as e:
                    logger.warning(f"CORS origin 처리 오류: {str(e)}")
                    # Fallback to wildcard for development
                    response.headers.add("Access-Control-Allow-Origin", "*")
        else:
            # Wildcard for development
            response.headers.add("Access-Control-Allow-Origin", "*")

        # Add standard CORS headers with error handling
        try:
            response.headers.add(
                "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
            )
            response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
            response.headers.add("Access-Control-Allow-Credentials", "true")
            response.headers.add("Vary", "Origin")
        except Exception as e:
            logger.warning(f"CORS 헤더 설정 오류: {str(e)}")

        # Set UTF-8 charset for JSON responses to properly handle Korean characters
        try:
            if response.content_type and response.content_type.startswith('application/json'):
                response.content_type = 'application/json; charset=utf-8'
        except Exception as e:
            logger.warning(f"Content-Type 설정 오류: {str(e)}")

    except Exception as e:
        logger.error(f"CORS 설정 중 예상치 못한 오류: {str(e)}")
        # Ensure basic CORS is still set even if there's an error
        try:
            response.headers.add("Access-Control-Allow-Origin", "*")
        except:
            pass

    return response


@tfidf_bp.route("/get_word_data", methods=["POST", "OPTIONS"])
def get_word_data():
    """Get word list data for manual selection mode"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        # Session validation
        try:
            session_id = get_or_create_session_id()
        except Exception as e:
            logger.error(f"세션 생성/조회 오류: {traceback.format_exc()}")
            return jsonify(create_error_response("세션 처리 중 오류가 발생했습니다", 500))

        # Request validation
        is_valid, error_msg = validate_request_data(["column_name"], request.form)
        if not is_valid:
            logger.warning(f"요청 검증 실패: {error_msg}")
            return jsonify(create_error_response(error_msg, 400))

        column_name = request.form.get("column_name").strip()

        # File validation
        is_valid, file_path, error_msg = handle_file_validation(session_id)
        if not is_valid:
            logger.warning(f"파일 검증 실패: {error_msg}")
            return jsonify(create_error_response(error_msg, 404))

        # Analysis execution with specific error handling
        try:
            analyzer = TFIDFAnalyzer()
            result = analyzer.analyze_tfidf(
                file_path, column_name, session_id, selection_type="manual"
            )
        except FileNotFoundError as e:
            logger.error(f"파일을 찾을 수 없음: {str(e)}")
            return jsonify(create_error_response("분석할 파일을 찾을 수 없습니다", 404))
        except PermissionError as e:
            logger.error(f"파일 접근 권한 오류: {str(e)}")
            return jsonify(create_error_response("파일에 접근할 권한이 없습니다", 403))
        except ValueError as e:
            logger.error(f"데이터 값 오류: {str(e)}")
            return jsonify(create_error_response(f"데이터 처리 오류: {str(e)}", 400))
        except MemoryError as e:
            logger.error(f"메모리 부족 오류: {str(e)}")
            return jsonify(create_error_response("파일이 너무 커서 처리할 수 없습니다", 413))

        # Check for analysis errors
        if isinstance(result, dict) and "error" in result:
            logger.warning(f"분석 오류: {result['error']}")
            return jsonify(create_error_response(result["error"], 400))

        # Validate result structure
        if not isinstance(result, dict):
            logger.error(f"예상치 못한 결과 형식: {type(result)}")
            return jsonify(create_error_response("분석 결과 형식이 올바르지 않습니다", 500))

        # Add download URLs with error handling
        try:
            if 'file_path' in result and result['file_path']:
                result["download_url"] = url_for(
                    "process.serve_file",
                    filepath=result["file_path"],
                    download="true",
                    session_id=session_id,
                    _external=True,
                )

            if 'output_file' in result and result['output_file']:
                result["csv_download_url"] = url_for(
                    "process.serve_file",
                    filepath=result["output_file"],
                    download="true",
                    session_id=session_id,
                    _external=True,
                )
        except Exception as e:
            logger.warning(f"다운로드 URL 생성 오류: {str(e)}")
            # Continue without download URLs rather than failing completely

        return jsonify(
            {
                "success": True,
                **result,
                "session_id": session_id,
            }
        )

    except Exception as e:
        logger.error(f"단어 데이터 처리 중 예상치 못한 오류: {traceback.format_exc()}")
        return jsonify(create_error_response(f"서버 내부 오류가 발생했습니다: {str(e)}", 500))


@tfidf_bp.route("/analyze", methods=["POST", "OPTIONS"])
def analyze():
    """Main TF-IDF analysis endpoint"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        # Session validation
        try:
            session_id = get_or_create_session_id()
        except Exception as e:
            logger.error(f"세션 생성/조회 오류: {traceback.format_exc()}")
            return jsonify(create_error_response("세션 처리 중 오류가 발생했습니다", 500))

        # Request validation
        is_valid, error_msg = validate_request_data(["column_name"], request.form)
        if not is_valid:
            logger.warning(f"요청 검증 실패: {error_msg}")
            return jsonify(create_error_response(error_msg, 400))

        column_name = request.form.get("column_name").strip()

        # File validation
        is_valid, file_path, error_msg = handle_file_validation(session_id)
        if not is_valid:
            logger.warning(f"파일 검증 실패: {error_msg}")
            return jsonify(create_error_response(error_msg, 404))

        # Parameter validation and sanitization
        try:
            selection_type = request.form.get("selection_type", "top_n").strip()
            if selection_type not in ["top_n", "manual"]:
                return jsonify(create_error_response("잘못된 선택 타입입니다. 'top_n' 또는 'manual'만 허용됩니다", 400))

            max_words_str = request.form.get("max_words", "50").strip()
            try:
                max_words = int(max_words_str)
                if max_words <= 0 or max_words > 1000:
                    return jsonify(create_error_response("최대 단어 수는 1-1000 사이여야 합니다", 400))
            except ValueError:
                return jsonify(create_error_response("최대 단어 수는 숫자여야 합니다", 400))

            cloud_shape = request.form.get("cloud_shape", "rectangle").strip()
            allowed_shapes = ["rectangle", "circle", "heart", "star"]
            if cloud_shape not in allowed_shapes:
                return jsonify(create_error_response(f"지원되지 않는 워드클라우드 모양입니다. 허용된 값: {', '.join(allowed_shapes)}", 400))

            cloud_color = request.form.get("cloud_color", "viridis").strip()
            allowed_colors = ["viridis", "plasma", "inferno", "magma", "rainbow"]
            if cloud_color not in allowed_colors:
                return jsonify(create_error_response(f"지원되지 않는 색상입니다. 허용된 값: {', '.join(allowed_colors)}", 400))

            selected_words = request.form.get("selected_words", None)
            if selected_words:
                selected_words = selected_words.strip()
                if selection_type == "manual" and not selected_words:
                    return jsonify(create_error_response("수동 선택 모드에서는 선택된 단어가 필요합니다", 400))

        except Exception as e:
            logger.error(f"매개변수 검증 오류: {str(e)}")
            return jsonify(create_error_response("요청 매개변수 처리 중 오류가 발생했습니다", 400))

        # Analysis execution with specific error handling
        try:
            analyzer = TFIDFAnalyzer()
            result = analyzer.analyze_tfidf(
                file_path,
                column_name,
                session_id,
                selection_type,
                str(max_words),
                cloud_shape,
                cloud_color,
                selected_words,
            )
        except FileNotFoundError as e:
            logger.error(f"파일을 찾을 수 없음: {str(e)}")
            return jsonify(create_error_response("분석할 파일을 찾을 수 없습니다", 404))
        except PermissionError as e:
            logger.error(f"파일 접근 권한 오류: {str(e)}")
            return jsonify(create_error_response("파일에 접근할 권한이 없습니다", 403))
        except ValueError as e:
            logger.error(f"데이터 값 오류: {str(e)}")
            return jsonify(create_error_response(f"데이터 처리 오류: {str(e)}", 400))
        except MemoryError as e:
            logger.error(f"메모리 부족 오류: {str(e)}")
            return jsonify(create_error_response("파일이 너무 커서 처리할 수 없습니다", 413))

        # Check for analysis errors
        if isinstance(result, dict) and "error" in result:
            logger.warning(f"분석 오류: {result['error']}")
            return jsonify(create_error_response(result["error"], 400))

        # Validate result structure
        if not isinstance(result, dict):
            logger.error(f"예상치 못한 결과 형식: {type(result)}")
            return jsonify(create_error_response("분석 결과 형식이 올바르지 않습니다", 500))

        # Add download URLs with error handling
        try:
            if 'file_path' in result and result['file_path']:
                result["download_url"] = url_for(
                    "process.serve_file",
                    filepath=result["file_path"],
                    download="true",
                    session_id=session_id,
                    _external=True,
                )

            if 'output_file' in result and result['output_file']:
                result["csv_download_url"] = url_for(
                    "process.serve_file",
                    filepath=result["output_file"],
                    download="true",
                    session_id=session_id,
                    _external=True,
                )
        except Exception as e:
            logger.warning(f"다운로드 URL 생성 오류: {str(e)}")
            # Continue without download URLs rather than failing completely

        return jsonify({"success": True, "session_id": session_id, **result})

    except Exception as e:
        logger.error(f"TF-IDF 분석 중 예상치 못한 오류: {traceback.format_exc()}")
        return jsonify(create_error_response(f"서버 내부 오류가 발생했습니다: {str(e)}", 500))


@tfidf_bp.route("/generate_wordcloud", methods=["POST", "OPTIONS"])
def generate_wordcloud():
    """Generate wordcloud from selected words"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        # Session validation
        try:
            session_id = get_or_create_session_id()
        except Exception as e:
            logger.error(f"세션 생성/조회 오류: {traceback.format_exc()}")
            return jsonify(create_error_response("세션 처리 중 오류가 발생했습니다", 500))

        # Request validation
        is_valid, error_msg = validate_request_data(["selected_words"], request.form)
        if not is_valid:
            logger.warning(f"요청 검증 실패: {error_msg}")
            return jsonify(create_error_response(error_msg, 400))

        # JSON validation for selected words
        selected_words_str = request.form.get("selected_words", "[]").strip()
        is_valid, word_list, error_msg = validate_json_data(selected_words_str, "선택된 단어")
        if not is_valid:
            logger.warning(f"JSON 검증 실패: {error_msg}")
            return jsonify(create_error_response(error_msg, 400))

        # Word list validation
        if not isinstance(word_list, list):
            return jsonify(create_error_response("선택된 단어는 배열 형태여야 합니다", 400))

        if len(word_list) < 10:
            return jsonify(create_error_response("최소 10개 이상의 단어를 선택해주세요", 400))

        if len(word_list) > 500:
            return jsonify(create_error_response("최대 500개까지의 단어만 처리할 수 있습니다", 400))

        # Validate individual words
        validated_words = []
        for i, word in enumerate(word_list):
            if not isinstance(word, str):
                return jsonify(create_error_response(f"단어 {i+1}번째 항목이 문자열이 아닙니다", 400))

            word = word.strip()
            if not word:
                continue  # Skip empty words

            if len(word) > 50:
                return jsonify(create_error_response(f"단어 '{word[:20]}...'가 너무 깁니다 (최대 50자)", 400))

            validated_words.append(word)

        if len(validated_words) < 10:
            return jsonify(create_error_response("유효한 단어가 10개 미만입니다", 400))

        # Parameter validation
        cloud_shape = request.form.get("cloud_shape", "rectangle").strip()
        allowed_shapes = ["rectangle", "circle", "heart", "star"]
        if cloud_shape not in allowed_shapes:
            return jsonify(create_error_response(f"지원되지 않는 워드클라우드 모양입니다. 허용된 값: {', '.join(allowed_shapes)}", 400))

        cloud_color = request.form.get("cloud_color", "viridis").strip()
        allowed_colors = ["viridis", "plasma", "inferno", "magma", "rainbow"]
        if cloud_color not in allowed_colors:
            return jsonify(create_error_response(f"지원되지 않는 색상입니다. 허용된 값: {', '.join(allowed_colors)}", 400))

        # Generate wordcloud with specific error handling
        try:
            wordcloud_generator = WordCloudGenerator()
            result = wordcloud_generator.generate_from_words(
                validated_words, session_id, cloud_shape, cloud_color
            )
        except MemoryError as e:
            logger.error(f"메모리 부족 오류: {str(e)}")
            return jsonify(create_error_response("워드클라우드 생성 중 메모리가 부족합니다. 단어 수를 줄여보세요", 413))
        except OSError as e:
            logger.error(f"파일 시스템 오류: {str(e)}")
            return jsonify(create_error_response("워드클라우드 파일 생성 중 오류가 발생했습니다", 500))
        except Exception as e:
            logger.error(f"워드클라우드 생성 오류: {str(e)}")
            return jsonify(create_error_response(f"워드클라우드 생성 중 오류가 발생했습니다: {str(e)}", 500))

        # Check for generation errors
        if isinstance(result, dict) and "error" in result:
            logger.warning(f"워드클라우드 생성 오류: {result['error']}")
            return jsonify(create_error_response(result["error"], 400))

        # Validate result structure
        if not isinstance(result, dict):
            logger.error(f"예상치 못한 결과 형식: {type(result)}")
            return jsonify(create_error_response("워드클라우드 생성 결과 형식이 올바르지 않습니다", 500))

        # Add download URL with error handling
        try:
            if 'file_path' in result and result['file_path']:
                result["download_url"] = url_for(
                    "process.serve_file",
                    filepath=result["file_path"],
                    download="true",
                    session_id=session_id,
                    _external=True,
                )
        except Exception as e:
            logger.warning(f"다운로드 URL 생성 오류: {str(e)}")
            # Continue without download URL rather than failing completely

        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                **result
            }
        )

    except Exception as e:
        logger.error(f"워드클라우드 생성 중 예상치 못한 오류: {traceback.format_exc()}")
        return jsonify(create_error_response(f"서버 내부 오류가 발생했습니다: {str(e)}", 500))


@tfidf_bp.route("/edit_words", methods=["POST", "OPTIONS"])
def edit_words():
    """Edit words and regenerate wordcloud"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        # Session validation
        try:
            session_id = get_or_create_session_id()
        except Exception as e:
            logger.error(f"세션 생성/조회 오류: {traceback.format_exc()}")
            return jsonify(create_error_response("세션 처리 중 오류가 발생했습니다", 500))

        # Request validation
        is_valid, error_msg = validate_request_data(["edited_words"], request.form)
        if not is_valid:
            logger.warning(f"요청 검증 실패: {error_msg}")
            return jsonify(create_error_response(error_msg, 400))

        # JSON validation for edited words
        edited_words_str = request.form.get("edited_words", "[]").strip()
        is_valid, word_list, error_msg = validate_json_data(edited_words_str, "편집된 단어")
        if not is_valid:
            logger.warning(f"JSON 검증 실패: {error_msg}")
            return jsonify(create_error_response(error_msg, 400))

        # Word list validation
        if not isinstance(word_list, list):
            return jsonify(create_error_response("편집된 단어는 배열 형태여야 합니다", 400))

        if len(word_list) < 10:
            return jsonify(create_error_response("최소 10개 이상의 단어가 필요합니다", 400))

        if len(word_list) > 500:
            return jsonify(create_error_response("최대 500개까지의 단어만 처리할 수 있습니다", 400))

        # Validate word structure - can be either strings or objects with "new" and "original" keys
        validated_words = []
        for i, word_item in enumerate(word_list):
            if isinstance(word_item, str):
                # Simple string word
                word = word_item.strip()
                if not word:
                    continue  # Skip empty words

                if len(word) > 50:
                    return jsonify(create_error_response(f"단어 '{word[:20]}...'가 너무 깁니다 (최대 50자)", 400))

                validated_words.append(word)

            elif isinstance(word_item, dict):
                # Word edit object with "new" and "original" keys
                if "new" not in word_item:
                    return jsonify(create_error_response(f"단어 {i+1}번째 항목에 'new' 키가 없습니다", 400))

                new_word = str(word_item["new"]).strip()
                if not new_word:
                    continue  # Skip empty words

                if len(new_word) > 50:
                    return jsonify(create_error_response(f"새 단어 '{new_word[:20]}...'가 너무 깁니다 (최대 50자)", 400))

                validated_words.append(new_word)
            else:
                return jsonify(create_error_response(f"단어 {i+1}번째 항목이 올바른 형식이 아닙니다", 400))

        if len(validated_words) < 10:
            return jsonify(create_error_response("유효한 단어가 10개 미만입니다", 400))

        # Parameter validation
        cloud_shape = request.form.get("cloud_shape", "rectangle").strip()
        allowed_shapes = ["rectangle", "circle", "heart", "star"]
        if cloud_shape not in allowed_shapes:
            return jsonify(create_error_response(f"지원되지 않는 워드클라우드 모양입니다. 허용된 값: {', '.join(allowed_shapes)}", 400))

        cloud_color = request.form.get("cloud_color", "viridis").strip()
        allowed_colors = ["viridis", "plasma", "inferno", "magma", "rainbow"]
        if cloud_color not in allowed_colors:
            return jsonify(create_error_response(f"지원되지 않는 색상입니다. 허용된 값: {', '.join(allowed_colors)}", 400))

        # Generate wordcloud with edited words and specific error handling
        try:
            wordcloud_generator = WordCloudGenerator()
            result = wordcloud_generator.generate_from_words(
                validated_words, session_id, cloud_shape, cloud_color, prefix="edited"
            )
        except MemoryError as e:
            logger.error(f"메모리 부족 오류: {str(e)}")
            return jsonify(create_error_response("워드클라우드 생성 중 메모리가 부족합니다. 단어 수를 줄여보세요", 413))
        except OSError as e:
            logger.error(f"파일 시스템 오류: {str(e)}")
            return jsonify(create_error_response("워드클라우드 파일 생성 중 오류가 발생했습니다", 500))
        except Exception as e:
            logger.error(f"워드클라우드 생성 오류: {str(e)}")
            return jsonify(create_error_response(f"워드클라우드 생성 중 오류가 발생했습니다: {str(e)}", 500))

        # Check for generation errors
        if isinstance(result, dict) and "error" in result:
            logger.warning(f"워드클라우드 생성 오류: {result['error']}")
            return jsonify(create_error_response(result["error"], 400))

        # Validate result structure
        if not isinstance(result, dict):
            logger.error(f"예상치 못한 결과 형식: {type(result)}")
            return jsonify(create_error_response("워드클라우드 생성 결과 형식이 올바르지 않습니다", 500))

        # Add download URL with error handling
        try:
            if 'file_path' in result and result['file_path']:
                result["download_url"] = url_for(
                    "process.serve_file",
                    filepath=result["file_path"],
                    download="true",
                    session_id=session_id,
                    _external=True,
                )
        except Exception as e:
            logger.warning(f"다운로드 URL 생성 오류: {str(e)}")
            # Continue without download URL rather than failing completely

        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                **result
            }
        )

    except Exception as e:
        logger.error(f"단어 편집 및 워드클라우드 생성 중 예상치 못한 오류: {traceback.format_exc()}")
        return jsonify(create_error_response(f"서버 내부 오류가 발생했습니다: {str(e)}", 500))
